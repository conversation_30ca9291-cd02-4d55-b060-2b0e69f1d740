# 📁 项目结构说明

## 🏗️ 文件结构概览

```
tp2t/
├── 📱 核心应用文件
│   ├── app.py                 # Streamlit Web应用主程序
│   ├── prompt_extend.py       # 核心Prompt扩展功能模块
│   └── qwen_vl_utils.py       # Qwen视觉模型工具函数
│
├── ⚙️ 配置与工具
│   ├── config.py              # 应用配置文件
│   ├── requirements.txt       # Python依赖列表
│   └── run.py                 # 应用启动脚本
│
├── 📚 文档与演示
│   ├── README.md              # 详细使用文档（英文）
│   ├── 快速开始.md            # 快速开始指南（中文）
│   ├── 项目结构说明.md        # 本文件
│   └── demo.py                # 功能演示脚本
│
└── 📂 示例文件
    └── examples/              # 示例图片目录（用户自添）
```

---

## 📝 文件详细说明

### 🎯 核心应用文件

#### `app.py` - Web应用主程序
- **功能**：Streamlit构建的Web界面
- **特性**：
  - 支持三种输入模式（纯文本、单图片、多图片）
  - 支持三种模型（DashScope、SiliconFlow、本地Qwen）
  - 多语言输出（中文/英文）
  - 直观的用户界面

#### `prompt_extend.py` - 核心功能模块
- **功能**：包含所有Prompt扩展类
- **包含的类**：
  - `PromptExpander`：基础抽象类
  - `DashScopePromptExpander`：阿里云API实现
  - `SiliconFlowPromptExpander`：SiliconFlow API实现
  - `QwenPromptExpander`：本地Qwen模型实现

#### `qwen_vl_utils.py` - 视觉工具函数
- **功能**：处理Qwen视觉模型的图片输入
- **作用**：解析和预处理多模态消息

### ⚙️ 配置与工具

#### `config.py` - 配置文件
- **功能**：集中管理应用配置
- **包含**：
  - 模型配置选项
  - API端点和参数
  - 文件上传限制
  - 使用示例

#### `requirements.txt` - 依赖文件
- **功能**：列出所有Python依赖包
- **包含**：Streamlit、PyTorch、Transformers等

#### `run.py` - 启动脚本
- **功能**：一键启动应用
- **特性**：
  - 自动检查依赖
  - 友好的启动信息
  - 错误处理

### 📚 文档与演示

#### `README.md` - 详细文档
- **语言**：英文
- **内容**：完整的使用说明、配置指南、故障排除

#### `快速开始.md` - 中文快速指南
- **语言**：中文
- **内容**：三步启动、常见场景、快速配置

#### `demo.py` - 演示脚本
- **功能**：命令行演示所有功能
- **包含**：
  - 纯文本扩展演示
  - 视觉扩展演示
  - 多语言输出演示
  - 多图片扩展演示

---

## 🚀 使用流程

### 1. 普通用户（推荐）
```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
python run.py

# 在浏览器中使用Web界面
```

### 2. 开发者
```bash
# 查看演示
python demo.py

# 直接启动Streamlit
streamlit run app.py

# 自定义配置
# 编辑 config.py
```

### 3. 高级用户
```bash
# 下载本地模型
# 配置 ./models/ 目录

# 设置环境变量
export DASH_API_KEY="your_key"

# 使用本地模型运行
```

---

## 🔧 扩展指南

### 添加新的模型支持
1. 在`prompt_extend.py`中继承`PromptExpander`类
2. 实现`extend()`和`extend_with_img()`方法
3. 在`config.py`中添加模型配置
4. 在`app.py`中添加UI选项

### 添加新的输入模式
1. 在`config.py`中添加示例
2. 在`app.py`中添加UI处理逻辑
3. 更新系统提示词（如需要）

### 自定义界面
1. 修改`app.py`中的Streamlit组件
2. 调整`config.py`中的界面配置
3. 更新CSS样式（如需要）

---

## 📞 技术支持

- **新手用户**：请先阅读`快速开始.md`
- **详细使用**：请参考`README.md`
- **功能演示**：运行`python demo.py`
- **问题反馈**：通过GitHub Issues

---

🎨 **祝您使用愉快！** 