# 🎨 智能Prompt优化器

一个功能强大的Web应用，支持文本、图片及多模态输入的智能Prompt扩展与优化工具。

## ✨ 功能特性

### 🎯 多种输入模式
- **纯文本模式**：输入简单的文本描述，自动扩展为详细的Prompt
- **单图片+文本模式**：结合图片内容进行Prompt优化
- **多图片+文本模式**：支持多张图片输入（特别适合视频生成的首尾帧描述）

### 🤖 多种模型支持
- **DashScope API**：阿里云灵积平台的qwen-plus和qwen-vl-max模型
- **SiliconFlow API**：支持DeepSeek-V3和Qwen2.5-VL系列模型
- **本地Qwen模型**：支持本地部署的Qwen2.5系列模型

### 🌍 多语言输出
- 中文输出：适合中文用户使用
- English输出：支持英文Prompt生成

### ⚙️ 灵活配置
- 随机种子控制：确保结果可复现
- 设备选择：支持CPU和GPU运行
- API密钥配置：安全的密钥管理

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置API密钥（可选）

如果使用API模式，可以设置环境变量：

```bash
# DashScope API
export DASH_API_KEY="your_dashscope_api_key"

# 或在应用界面中直接输入API密钥
```

### 3. 运行应用

```bash
streamlit run app.py
```

应用将在浏览器中自动打开，默认地址为：`http://localhost:8501`

## 📖 使用指南

### 基本使用流程

1. **选择模型类型**：在左侧边栏选择DashScope API、SiliconFlow API或本地Qwen模型
2. **选择输入模式**：根据需求选择纯文本、单图片+文本或多图片+文本
3. **配置参数**：设置输出语言、随机种子等参数
4. **输入内容**：在左侧输入区域填写Prompt文本，如需要请上传图片
5. **开始优化**：点击"🚀 开始优化Prompt"按钮
6. **查看结果**：在右侧查看优化后的Prompt结果

### 使用场景示例

#### 📝 纯文本模式
- **输入**：`夏日海滩度假风格，一只戴着墨镜的白色猫咪坐在冲浪板上`
- **输出**：详细的画面描述，包含服装、表情、背景、镜头角度等细节

#### 🖼️ 单图片+文本模式
- **适用场景**：根据现有图片进行Prompt优化
- **使用方法**：上传一张图片，输入简单描述，系统会结合图片内容生成详细Prompt

#### 📹 多图片+文本模式
- **适用场景**：视频生成的Prompt优化
- **使用方法**：上传多张图片（建议为视频的第一帧和最后一帧），输入运动描述

## 🔧 技术特性

### 支持的图片格式
- PNG (.png)
- JPEG (.jpg, .jpeg)

### 模型配置

#### DashScope API
- **文本模型**：qwen-plus
- **视觉模型**：qwen-vl-max

#### SiliconFlow API
- **文本模型**：deepseek-ai/DeepSeek-V3, Qwen/Qwen2.5-72B-Instruct
- **视觉模型**：Qwen/Qwen2.5-VL-72B-Instruct, Qwen/Qwen2.5-VL-32B-Instruct

#### 本地模型
- 支持Qwen2.5系列模型
- 支持GPU和CPU运行
- 支持Flash Attention 2加速

## 🛠️ 高级配置

### 环境变量配置

```bash
# DashScope配置
export DASH_API_KEY="your_api_key"
export DASH_API_URL="https://dashscope.aliyuncs.com/api/v1"

# CUDA设备配置（如果使用GPU）
export CUDA_VISIBLE_DEVICES="0"
```

### 本地模型部署

1. 下载Qwen模型到本地目录
2. 在应用中配置模型路径
3. 选择合适的设备（GPU推荐）

## 📋 系统要求

### 最低要求
- Python 3.8+
- 4GB RAM
- 稳定的网络连接（API模式）

### 推荐配置（本地模型）
- Python 3.9+
- 16GB+ RAM
- NVIDIA GPU with 8GB+ VRAM
- CUDA 11.8+

## 🔍 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证API配额是否充足

2. **本地模型加载失败**
   - 检查模型路径是否正确
   - 确认显存是否充足
   - 验证CUDA环境配置

3. **图片上传失败**
   - 检查图片格式是否支持
   - 确认图片大小合理（建议<10MB）
   - 验证图片文件是否损坏

## 📄 许可证

本项目基于MIT许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 联系我们

如果您有任何问题或建议，请通过GitHub Issues联系我们。 