"""
Qwen VL Utils - Vision processing utilities for Qwen VL models
"""

from typing import List, Dict, Any, Union, Optional, Tuple
from PIL import Image
import torch


def process_vision_info(messages: List[Dict[str, Any]]) -> Tuple[List[Image.Image], List]:
    """
    Process vision information from chat messages.
    
    Args:
        messages: List of chat messages that may contain images
        
    Returns:
        Tuple of (image_inputs, video_inputs)
    """
    image_inputs = []
    video_inputs = []
    
    for message in messages:
        content = message.get("content", [])
        if isinstance(content, str):
            continue
            
        for item in content:
            if isinstance(item, dict):
                if "image" in item:
                    # Handle image input
                    image_data = item["image"]
                    if isinstance(image_data, str):
                        # If it's a file path or URL
                        try:
                            img = Image.open(image_data).convert('RGB')
                            image_inputs.append(img)
                        except:
                            pass
                    elif isinstance(image_data, Image.Image):
                        # If it's already a PIL Image
                        image_inputs.append(image_data.convert('RGB'))
                elif "video" in item:
                    # Handle video input (placeholder for future implementation)
                    video_inputs.append(item["video"])
    
    return image_inputs, video_inputs 