#!/usr/bin/env python3
"""
演示脚本 - 智能Prompt优化器
展示各种API和模型的使用方法
"""

import sys
import os
from pathlib import Path

# 添加当前目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from prompt_extend import (
    DashScopePromptExpander, 
    QwenPromptExpander, 
    SiliconFlowPromptExpander
)

def demo_text_extension():
    """演示纯文本扩展功能"""
    print("\n🎯 演示：纯文本Prompt扩展")
    print("=" * 60)
    
    # 测试用的Prompt
    test_prompts = [
        "夏日海滩度假风格，一只戴着墨镜的白色猫咪坐在冲浪板上",
        "雨夜城市街头，霓虹灯倒映在湿润的地面上",
        "古典园林，春风吹过桃花满树"
    ]
    
    # 使用SiliconFlow API进行演示（因为有默认API key）
    try:
        print("📡 使用SiliconFlow API (DeepSeek-V3)")
        expander = SiliconFlowPromptExpander(
            model_name="deepseek-ai/DeepSeek-V3",
            is_vl=False
        )
        
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\n📝 测试 {i}: {prompt}")
            print("-" * 40)
            
            result = expander(prompt, tar_lang="zh", seed=100)
            
            if result.status:
                print(f"✅ 优化成功:")
                print(f"🎨 结果: {result.prompt}")
            else:
                print(f"❌ 优化失败: {result.message}")
                
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def demo_vision_extension():
    """演示视觉+文本扩展功能"""
    print("\n🖼️ 演示：图片+文本Prompt扩展")
    print("=" * 60)
    
    # 检查是否有示例图片
    examples_dir = Path("examples")
    if not examples_dir.exists():
        print("⚠️ 示例图片目录不存在，跳过视觉演示")
        return
    
    # 查找示例图片
    image_files = list(examples_dir.glob("*.jpg")) + list(examples_dir.glob("*.png"))
    if not image_files:
        print("⚠️ 未找到示例图片，跳过视觉演示")
        return
    
    print(f"📸 找到 {len(image_files)} 张示例图片")
    
    try:
        # 使用SiliconFlow API的视觉模型
        print("📡 使用SiliconFlow API (Qwen2.5-VL-72B)")
        expander = SiliconFlowPromptExpander(
            model_name="Qwen/Qwen2.5-VL-72B-Instruct",
            is_vl=True
        )
        
        # 测试第一张图片
        test_image = image_files[0]
        test_prompt = "根据图片内容，创作一个详细的场景描述"
        
        print(f"\n📷 使用图片: {test_image.name}")
        print(f"📝 提示词: {test_prompt}")
        print("-" * 40)
        
        result = expander(
            prompt=test_prompt,
            tar_lang="zh",
            image=str(test_image),
            seed=100
        )
        
        if result.status:
            print(f"✅ 优化成功:")
            print(f"🎨 结果: {result.prompt}")
        else:
            print(f"❌ 优化失败: {result.message}")
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def demo_multi_image_extension():
    """演示多图片扩展功能"""
    print("\n📹 演示：多图片+文本Prompt扩展")
    print("=" * 60)
    
    # 检查是否有示例图片
    examples_dir = Path("examples")
    if not examples_dir.exists():
        print("⚠️ 示例图片目录不存在，跳过多图片演示")
        return
    
    # 查找示例图片
    image_files = list(examples_dir.glob("*.jpg")) + list(examples_dir.glob("*.png"))
    if len(image_files) < 2:
        print("⚠️ 示例图片不足2张，跳过多图片演示")
        return
    
    try:
        # 使用SiliconFlow API的视觉模型
        print("📡 使用SiliconFlow API (Qwen2.5-VL-72B)")
        expander = SiliconFlowPromptExpander(
            model_name="Qwen/Qwen2.5-VL-72B-Instruct",
            is_vl=True
        )
        
        # 使用前两张图片
        test_images = [str(image_files[0]), str(image_files[1])]
        test_prompt = "镜头从近景慢慢拉远，展现完整的场景变化"
        
        print(f"\n📷 使用图片: {[img.split('/')[-1] for img in test_images]}")
        print(f"📝 提示词: {test_prompt}")
        print("-" * 40)
        
        result = expander(
            prompt=test_prompt,
            tar_lang="zh",
            image=test_images,
            seed=100
        )
        
        if result.status:
            print(f"✅ 优化成功:")
            print(f"🎨 结果: {result.prompt}")
        else:
            print(f"❌ 优化失败: {result.message}")
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def demo_different_languages():
    """演示不同语言输出"""
    print("\n🌍 演示：多语言输出")
    print("=" * 60)
    
    test_prompt = "一只可爱的小猫在花园里玩耍"
    
    try:
        expander = SiliconFlowPromptExpander(
            model_name="deepseek-ai/DeepSeek-V3",
            is_vl=False
        )
        
        # 中文输出
        print("🇨🇳 中文输出:")
        result_zh = expander(test_prompt, tar_lang="zh", seed=100)
        if result_zh.status:
            print(f"✅ {result_zh.prompt}")
        else:
            print(f"❌ {result_zh.message}")
        
        print("\n" + "-" * 40)
        
        # 英文输出  
        print("🇺🇸 英文输出:")
        result_en = expander(test_prompt, tar_lang="en", seed=100)
        if result_en.status:
            print(f"✅ {result_en.prompt}")
        else:
            print(f"❌ {result_en.message}")
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def main():
    """主函数"""
    print("🎨 智能Prompt优化器 - 功能演示")
    print("=" * 60)
    print("本演示将展示各种功能的使用方法")
    print("注意: 本演示使用预设的API密钥，实际使用时请配置您自己的密钥")
    
    # 运行各种演示
    demo_text_extension()
    demo_different_languages()
    demo_vision_extension()
    demo_multi_image_extension()
    
    print("\n🎉 演示完成!")
    print("=" * 60)
    print("要启动Web界面，请运行:")
    print("  python run.py")
    print("或者:")
    print("  streamlit run app.py")

if __name__ == "__main__":
    main() 