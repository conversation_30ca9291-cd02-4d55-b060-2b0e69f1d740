# 🚀 服务器部署说明

## 📋 部署概述

本文档指导您在远程服务器上部署**智能Prompt优化器**，为内网用户提供Web服务。

---

## 🛠️ 环境要求

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 18.04+)
- **Python**: 3.8 或更高版本
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 10GB 可用空间

### 网络要求
- 服务器需要访问外网（用于API调用）
- 确保内网用户可以访问服务器IP
- 开放指定端口（默认8501）

---

## 📦 安装步骤

### 1. 克隆项目
```bash
cd /data2/jevon/test/tp2t
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 检查环境
```bash
python -c "import streamlit, PIL, torch; print('✅ 环境检查通过')"
```

---

## 🚀 启动服务

### 方式一：标准启动（推荐）
```bash
python start_server.py
```

### 方式二：自定义配置启动
```bash
# 指定端口和其他选项
python start_server.py --port 8080 --debug

# 查看所有选项
python start_server.py --help
```

### 方式三：后台运行
```bash
# 使用nohup后台运行
nohup python start_server.py > server.log 2>&1 &

# 或使用screen
screen -S tp2t
python start_server.py
# 按 Ctrl+A, D 脱离screen会话
```

---

## 🌐 访问服务

### 内网访问地址
- **本机访问**: `http://localhost:8501`
- **内网访问**: `http://[服务器内网IP]:8501`
- **外网访问**: `http://[服务器公网IP]:8501`

### 查看服务器IP
```bash
# 查看内网IP
hostname -I

# 查看公网IP
curl ifconfig.me
```

---

## 🔧 配置选项

### 启动参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--host` | 0.0.0.0 | 监听地址 |
| `--port` | 8501 | 监听端口 |
| `--max-upload-size` | 200 | 最大上传文件大小(MB) |
| `--debug` | False | 开启调试模式 |

### 防火墙配置
```bash
# Ubuntu/Debian
sudo ufw allow 8501

# CentOS/RHEL
sudo firewall-cmd --add-port=8501/tcp --permanent
sudo firewall-cmd --reload
```

---

## 🔒 安全建议

### 1. 端口安全
- 仅开放必要端口
- 使用非标准端口
- 配置防火墙规则

### 2. 网络安全
```bash
# 仅允许内网访问
python start_server.py --host *************

# 使用反向代理 (Nginx示例)
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8501;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 访问控制
- 设置复杂的API密钥
- 监控访问日志
- 定期更新依赖

---

## 📊 监控和管理

### 查看运行状态
```bash
# 查看进程
ps aux | grep streamlit

# 查看端口占用
netstat -tulpn | grep :8501

# 查看日志
tail -f server.log
```

### 重启服务
```bash
# 找到进程ID
ps aux | grep start_server.py

# 停止服务
kill [PID]

# 重新启动
python start_server.py
```

---

## 🎯 用户使用指南

### 内网用户访问步骤
1. 打开浏览器
2. 输入地址：`http://[服务器IP]:8501`
3. 选择模型类型和输入模式
4. 输入Prompt文本或上传图片
5. 点击"开始优化Prompt"
6. 查看优化结果

### 功能说明
- **纯文本模式**: 优化文本描述
- **单图片模式**: 结合图片生成描述
- **多图片模式**: 生成动态场景描述
- **多语言支持**: 中文/英文输出
- **多用户支持**: 同时为多个用户提供服务

---

## ❓ 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口占用
sudo lsof -i :8501

# 检查Python版本
python --version

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

#### 2. 内网无法访问
```bash
# 检查防火墙
sudo ufw status

# 检查监听地址
netstat -tulpn | grep :8501

# 测试连接
telnet [服务器IP] 8501
```

#### 3. API调用失败
- 检查网络连接
- 验证API密钥
- 查看错误日志

#### 4. 内存不足
```bash
# 查看内存使用
free -h

# 监控进程内存
top -p [PID]

# 清理缓存
sync && echo 3 > /proc/sys/vm/drop_caches
```

---

## 📈 性能优化

### 1. 硬件优化
- 增加内存
- 使用SSD硬盘
- 配置GPU（本地模型）

### 2. 软件优化
```bash
# 使用更快的Python解释器
pip install uvloop

# 启用压缩
python start_server.py --server.enableStaticServing=true
```

### 3. 网络优化
- 使用CDN
- 配置负载均衡
- 启用gzip压缩

---

## 🔄 更新和维护

### 更新代码
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

### 定期维护
- 清理临时文件
- 检查日志大小
- 更新系统包
- 备份配置文件

---

## 📞 技术支持

### 获取帮助
- 查看项目文档：`README.md`
- 运行演示程序：`python demo.py`
- 检查配置文件：`config.py`

### 联系方式
如有问题，请提供以下信息：
- 操作系统版本
- Python版本
- 错误日志
- 网络环境

---

## 🎉 部署成功

恭喜！您已成功部署智能Prompt优化器服务。现在内网用户可以通过浏览器访问并使用这个强大的工具了。

**记住**：
- 定期检查服务状态
- 监控系统资源使用
- 保持依赖包更新
- 备份重要配置

祝您使用愉快！ 🚀 