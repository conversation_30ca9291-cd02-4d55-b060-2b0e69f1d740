# 🚀 快速开始指南

## 🎯 三步启动应用

### 第一步：安装依赖
```bash
pip install -r requirements.txt
```

### 第二步：启动应用
```bash
python run.py
```
或者
```bash
streamlit run app.py
```

### 第三步：打开浏览器
应用会自动在浏览器中打开，地址通常是：`http://localhost:8501`

---

## 💡 使用场景

### 🎨 纯文本模式
**适用场景**：快速扩展简单的文本描述
- 输入：`一只猫在海边`
- 输出：详细的画面描述，包含猫的外观、海边环境、镜头角度等

### 🖼️ 单图片模式  
**适用场景**：基于现有图片优化描述
- 上传一张图片
- 输入简单描述
- 系统结合图片内容生成详细Prompt

### 📹 多图片模式
**适用场景**：视频生成的动态描述
- 上传多张图片（建议为视频首尾帧）
- 描述镜头运动
- 生成包含动态变化的Prompt

---

## ⚙️ 快速配置

### API模式（推荐新手）
1. 选择"SiliconFlow API"（已内置测试密钥）
2. 或选择"DashScope API"并输入您的密钥
3. 开始使用

### 本地模式（适合高级用户）
1. 下载Qwen模型到`./models/`目录
2. 选择"本地Qwen模型"
3. 配置模型路径和设备

---

## 🎪 功能演示

运行演示脚本查看所有功能：
```bash
python demo.py
```

---

## ❓ 遇到问题？

### 常见问题
1. **依赖安装失败**：确保Python版本≥3.8
2. **API调用失败**：检查网络连接和API密钥
3. **本地模型加载失败**：确认显存充足

### 获取帮助
- 查看详细文档：`README.md`
- 运行演示了解功能：`python demo.py`
- 检查配置文件：`config.py`

---

🎉 **开始您的创意之旅吧！** 