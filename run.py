#!/usr/bin/env python3
"""
启动脚本 - 智能Prompt优化器
"""

import os
import sys
import subprocess
import argparse
import socket
from pathlib import Path

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 创建一个UDP套接字来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import streamlit
        import PIL
        import dashscope
        import requests
        print("✅ 核心依赖检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='智能Prompt优化器启动脚本')
    parser.add_argument('--host', default='0.0.0.0', help='服务器监听地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8502, help='服务器端口 (默认: 8502)')
    parser.add_argument('--headless', action='store_true', help='无头模式，不自动打开浏览器')
    parser.add_argument('--debug', action='store_true', help='开启调试模式')
    
    args = parser.parse_args()
    
    print("🎨 智能Prompt优化器")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    app_path = current_dir / "app.py"
    
    if not app_path.exists():
        print(f"❌ 找不到应用文件: {app_path}")
        sys.exit(1)
    
    # 获取本机IP地址
    local_ip = get_local_ip()
    
    print("🚀 正在启动Web应用...")
    print(f"🌐 服务器地址: {args.host}")
    print(f"🔌 监听端口: {args.port}")
    
    if args.host == '0.0.0.0':
        print(f"📱 本机访问: http://localhost:{args.port}")
        print(f"🌐 内网访问: http://{local_ip}:{args.port}")
        print(f"💡 内网用户可以通过服务器IP访问: http://[服务器IP]:{args.port}")
    else:
        print(f"🔗 访问地址: http://{args.host}:{args.port}")
    
    print("⏹️  按 Ctrl+C 停止应用")
    print("🔧 使用 --help 查看更多启动选项")
    print("=" * 60)
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        str(app_path),
        "--server.address", args.host,
        "--server.port", str(args.port),
        "--server.headless", str(args.headless).lower(),
        "--browser.gatherUsageStats", "false",
        "--server.enableCORS", "false",
        "--server.enableXsrfProtection", "false"
    ]
    
    if args.debug:
        cmd.extend(["--logger.level", "debug"])
    
    # 启动Streamlit应用
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 