# 🔧 问题解决记录

## 问题描述

**时间**: 2025-05-23 10:48  
**问题**: 网页端报错 `ModuleNotFoundError: No module named 'dashscope'`

### 错误详情
```
ModuleNotFoundError: No module named 'dashscope'
Traceback:
File "/data2/jevon/test/tp2t/app.py", line 14, in <module>
    from prompt_extend import (
File "/data2/jevon/test/tp2t/prompt_extend.py", line 14, in <module>
    import dashscope
```

---

## 问题原因

1. **依赖安装不完整**: 初始部署时只安装了基本依赖 (`streamlit`, `Pillow`, `requests`)
2. **缺少项目特定依赖**: `dashscope` 模块是项目核心功能所需，但未被安装
3. **requirements.txt 未完全安装**: 虽然项目包含完整的依赖列表，但初始部署时未完全安装

---

## 解决步骤

### 1. 停止服务
```bash
echo "4" | ./快速部署.sh
```

### 2. 安装完整依赖
```bash
pip3 install -r requirements.txt
```

### 3. 验证依赖安装
```bash
# 验证 dashscope
python3 -c "import dashscope; print('✅ dashscope 导入成功')"

# 验证应用模块
python3 -c "import sys; sys.path.append('/data2/jevon/test/tp2t'); from prompt_extend import DashScopePromptExpander, QwenPromptExpander, SiliconFlowPromptExpander; print('✅ 所有模块导入成功')"
```

### 4. 重新启动服务
```bash
echo "2" | ./快速部署.sh
```

### 5. 验证服务状态
```bash
echo "3" | ./快速部署.sh
```

---

## 预防措施

### 1. 更新快速部署脚本
已增强 `快速部署.sh`，现在包含：
- ✅ 完整的依赖检查（包括 `dashscope`）
- ✅ 自动依赖安装
- ✅ 应用模块导入验证
- ✅ 新增选项 5：重新安装依赖

### 2. 依赖检查清单
部署前确保以下模块可用：
- [x] `streamlit` - Web框架
- [x] `PIL` (Pillow) - 图像处理
- [x] `requests` - HTTP请求
- [x] `dashscope` - 阿里云API
- [x] `torch` - PyTorch（如使用本地模型）
- [x] `transformers` - 模型库
- [x] `flash-attn` - 注意力优化

### 3. 自动化验证
```bash
# 完整依赖验证脚本
python3 -c "
import streamlit
import PIL
import dashscope
import requests
import torch
import transformers
print('✅ 所有核心依赖验证通过')
"
```

---

## 常见问题解决

### Q1: 如何重新安装所有依赖？
```bash
# 方法1: 使用快速部署脚本
./快速部署.sh  # 选择选项 5

# 方法2: 手动安装
pip3 install -r requirements.txt --force-reinstall
```

### Q2: 如何检查特定模块是否安装？
```bash
python3 -c "import 模块名; print('已安装')"
```

### Q3: 安装失败怎么办？
1. 检查网络连接
2. 更新 pip: `pip3 install --upgrade pip`
3. 清理缓存: `pip3 cache purge`
4. 重试安装

### Q4: 如何查看安装的依赖版本？
```bash
pip3 list | grep -E "(streamlit|dashscope|torch)"
```

---

## 监控建议

### 1. 定期健康检查
```bash
# 每日检查脚本
./check_status.sh
./快速部署.sh  # 选择选项 3
```

### 2. 依赖版本管理
```bash
# 生成当前环境的依赖列表
pip3 freeze > current_requirements.txt

# 比较差异
diff requirements.txt current_requirements.txt
```

### 3. 日志监控
```bash
# 监控错误关键词
tail -f server.log | grep -i "error\|exception\|failed"
```

---

## 经验总结

### ✅ 成功解决
- 模块导入错误已修复
- 服务正常运行
- 增强了自动化检查

### 📝 学到的经验
1. **完整部署的重要性**: 初始部署应安装所有依赖
2. **自动化验证**: 部署脚本应包含完整的环境验证
3. **错误处理**: 提供明确的错误提示和解决方案
4. **文档记录**: 及时记录问题和解决方案

### 🔮 未来改进
1. 添加依赖版本锁定
2. 实现环境一致性检查
3. 增加自动恢复机制
4. 完善监控和告警

---

## 状态确认

**当前状态**: ✅ 已解决  
**服务状态**: ✅ 正常运行  
**访问地址**: http://**************:8501  
**验证时间**: 2025-05-23 10:49

**最后更新**: 2025-05-23 10:50 